"""
大纲生成API路由模块
提供文档大纲生成的REST API接口
"""
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from fastapi.responses import JSONResponse
from typing import Optional
from pathlib import Path

from ...core.deps import (
    validate_upload_file, 
    save_upload_file, 
    read_text_file,
    generate_task_id,
    get_current_settings
)
from ...core.config import Settings
from ...core.logging import get_logger
from ...schemas.outline import (
    OutlineGenerateResponse, 
    OutlineTaskQuery, 
    ErrorResponse,
    TaskStatus
)
from ...services.outline_service import outline_service
from ...utils.fileio import file_utils
from ...utils.idgen import filename_generator, path_generator
from ...utils.timers import async_timer, performance_monitor
from ...constants.paths import UPLOADS_DIR, OUTLINES_DIR

logger = get_logger("outline_api")

router = APIRouter(prefix="/outline", tags=["大纲生成"])

# 存储任务状态的简单内存存储（生产环境应使用数据库）
task_storage = {}


@router.post(
    "/generate",
    response_model=OutlineGenerateResponse,
    summary="生成文档大纲",
    description="上传Markdown文档并生成结构化大纲"
)
async def generate_outline(
    file: UploadFile = File(..., description="要处理的Markdown文件"),
    custom_prompt: Optional[str] = Form(None, description="自定义提示词"),
    include_refine: bool = Form(True, description="是否进行大纲精简"),
    model_name: Optional[str] = Form(None, description="指定模型名称"),
    settings: Settings = Depends(get_current_settings)
):
    """生成文档大纲"""
    
    task_id = generate_task_id()
    
    try:
        async with async_timer(f"outline_generation_{task_id}") as timer:
            logger.info(f"开始处理大纲生成请求 - 任务ID: {task_id}, 文件: {file.filename}")
            
            # 验证上传文件
            validated_file = await validate_upload_file(file, settings)
            
            # 生成安全的文件名
            upload_filename = filename_generator.generate_upload_filename(
                original_filename=validated_file.filename,
                task_id=task_id
            )
            
            # 生成上传路径
            upload_path = path_generator.generate_upload_path(
                base_dir=UPLOADS_DIR,
                filename=upload_filename
            )
            
            # 确保目录存在
            upload_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存上传文件
            file_size = await save_upload_file(validated_file, upload_path)
            
            # 读取文件内容
            file_content = await file_utils.read_text_file_safe(upload_path)
            
            # 更新任务状态
            task_storage[task_id] = {
                "status": TaskStatus.PROCESSING,
                "original_filename": validated_file.filename,
                "upload_path": str(upload_path),
                "file_size": file_size,
                "created_at": timer.start_time
            }
            
            logger.info(f"文件处理完成，开始生成大纲 - 任务ID: {task_id}")
            
            # 调用大纲生成服务
            result = await outline_service.process_outline_generation(
                file_content=file_content,
                original_filename=validated_file.filename,
                custom_prompt=custom_prompt,
                include_refine=include_refine,
                model_name=model_name
            )
            
            # 更新任务存储
            task_storage[task_id].update({
                "status": result.status,
                "result": result,
                "completed_at": timer.get_elapsed()
            })
            
            # 记录性能指标
            performance_monitor.record_timing(
                "outline_generation",
                await timer.get_elapsed(),
                task_id=task_id,
                file_size=file_size,
                filename=validated_file.filename,
                include_refine=include_refine,
                model_name=model_name or settings.outline_model
            )
            
            logger.info(f"大纲生成完成 - 任务ID: {task_id}, 状态: {result.status}")
            
            # 设置原始文件路径
            result.original_file_path = str(upload_path)
            
            return result
            
    except HTTPException:
        # 更新任务状态为失败
        if task_id in task_storage:
            task_storage[task_id]["status"] = TaskStatus.FAILED
        raise
    except Exception as e:
        logger.error(f"大纲生成过程中发生错误 - 任务ID: {task_id}, 错误: {str(e)}")
        
        # 更新任务状态为失败
        if task_id in task_storage:
            task_storage[task_id]["status"] = TaskStatus.FAILED
            task_storage[task_id]["error"] = str(e)
        
        raise HTTPException(
            status_code=500,
            detail=f"大纲生成失败: {str(e)}"
        )


@router.get(
    "/task/{task_id}",
    response_model=OutlineTaskQuery,
    summary="查询任务状态",
    description="根据任务ID查询大纲生成任务的状态和结果"
)
async def get_task_status(task_id: str):
    """查询任务状态"""
    
    logger.info(f"查询任务状态 - 任务ID: {task_id}")
    
    if task_id not in task_storage:
        raise HTTPException(
            status_code=404,
            detail=f"任务不存在: {task_id}"
        )
    
    task_data = task_storage[task_id]
    
    # 构建响应
    response = OutlineTaskQuery(
        task_id=task_id,
        status=task_data["status"],
        message=f"任务状态: {task_data['status'].value}",
        original_filename=task_data.get("original_filename"),
        file_size=task_data.get("file_size"),
        created_at=task_data.get("created_at", 0)
    )
    
    # 如果任务完成，添加结果数据
    if task_data["status"] == TaskStatus.COMPLETED and "result" in task_data:
        result = task_data["result"]
        response.outline_content = result.outline_content
        response.outline_file_path = result.outline_file_path
        response.processing_time = result.processing_time
        response.completed_at = result.completed_at
    
    # 如果任务失败，添加错误信息
    elif task_data["status"] == TaskStatus.FAILED and "error" in task_data:
        response.error_message = task_data["error"]
    
    return response


@router.get(
    "/tasks",
    summary="获取任务列表",
    description="获取所有任务的状态列表"
)
async def list_tasks():
    """获取任务列表"""

    logger.info("获取任务列表")

    tasks = []
    for task_id, task_data in task_storage.items():
        # 处理时间戳，确保可以 JSON 序列化
        created_at = task_data.get("created_at")
        completed_at = task_data.get("completed_at")

        if isinstance(created_at, float):
            from datetime import datetime
            created_at = datetime.fromtimestamp(created_at).isoformat()
        elif hasattr(created_at, 'isoformat'):
            created_at = created_at.isoformat()

        if isinstance(completed_at, float):
            from datetime import datetime
            completed_at = datetime.fromtimestamp(completed_at).isoformat()
        elif hasattr(completed_at, 'isoformat'):
            completed_at = completed_at.isoformat()

        task_info = {
            "task_id": task_id,
            "status": task_data["status"].value,
            "original_filename": task_data.get("original_filename"),
            "created_at": created_at,
            "completed_at": completed_at
        }
        tasks.append(task_info)

    return {
        "total": len(tasks),
        "tasks": tasks
    }


@router.delete(
    "/task/{task_id}",
    summary="删除任务",
    description="删除指定的任务记录"
)
async def delete_task(task_id: str):
    """删除任务"""
    
    logger.info(f"删除任务 - 任务ID: {task_id}")
    
    if task_id not in task_storage:
        raise HTTPException(
            status_code=404,
            detail=f"任务不存在: {task_id}"
        )
    
    # 删除任务记录
    del task_storage[task_id]
    
    return {
        "message": f"任务已删除: {task_id}"
    }


@router.get(
    "/metrics",
    summary="获取性能指标",
    description="获取大纲生成的性能统计信息"
)
async def get_metrics():
    """获取性能指标"""
    
    logger.info("获取性能指标")
    
    metrics = performance_monitor.get_metrics()
    
    return {
        "performance_metrics": metrics,
        "active_tasks": len([
            task for task in task_storage.values() 
            if task["status"] == TaskStatus.PROCESSING
        ]),
        "total_tasks": len(task_storage)
    }
